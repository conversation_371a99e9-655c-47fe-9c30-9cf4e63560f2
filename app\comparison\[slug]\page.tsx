import { Metada<PERSON> } from "next";
import { notFound } from "next/navigation";
import { getComparisonBySlug, getComparisons } from "@/content/lib";
import ComparisonHero from "../components/ComparisonHero";
import ComparisonProblem from "../components/ComparisonProblem";
import ComparisonFeatures from "../components/ComparisonFeatures";
import ComparisonPricing from "../components/ComparisonPricing";
import ComparisonReviews from "../components/ComparisonReviews";
import ComparisonFAQ from "../components/ComparisonFAQ";

interface ComparisonPageProps {
  params: {
    slug: string;
  };
}

export async function generateStaticParams() {
  const comparisons = getComparisons();
  return comparisons.map((comparison) => ({
    slug: comparison.slug,
  }));
}

export async function generateMetadata({
  params,
}: ComparisonPageProps): Promise<Metadata> {
  try {
    const comparison = await getComparisonBySlug(params.slug);

    return {
      title: comparison.title,
      description: comparison.description,
      openGraph: {
        title: comparison.title,
        description: comparison.description,
        url: `https://tryinloop.com/comparison/${params.slug}`,
        images: [
          {
            url: `https://tryinloop.com/meta-images/comparison-${params.slug}.png`,
            width: 800,
            height: 400,
          },
        ],
      },
      alternates: comparison.canonical
        ? { canonical: `https://tryinloop.com/comparison/${params.slug}` }
        : undefined,
    };
  } catch {
    return {
      title: "Comparison Not Found",
      description: "The requested comparison page could not be found.",
    };
  }
}

export default async function ComparisonPage({ params }: ComparisonPageProps) {
  try {
    const comparison = await getComparisonBySlug(params.slug);

    return (
      <main className="relative mt-16">
        <ComparisonHero comparison={comparison} />
        <ComparisonProblem comparison={comparison} />
        <ComparisonFeatures comparison={comparison} />
        <ComparisonPricing comparison={comparison} />
        <ComparisonReviews comparison={comparison} />
        <ComparisonFAQ comparison={comparison} />
      </main>
    );
  } catch {
    notFound();
  }
}
